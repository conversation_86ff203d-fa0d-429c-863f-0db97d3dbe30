import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import { useCallback, useEffect, useMemo, useState } from "react";
import styled from "styled-components";

import { PortalHospital } from "@/api/generated/types";
import { GET_HOSPITAL_BY_ID } from "@/api/hospital";
import { LoadingOverlay } from "@/components/atoms/LoadingOverlay";
import { PAGE_PATH } from "@/constants";
import { TREATMENT_DEPARTMENT_METHOD_TYPE } from "@/constants/hospital";
import { ReservationType } from "@/constants/reservation";
import { useCustomQuery } from "@/hooks/useCustomQuery";
import { useGlobalContext } from "@/providers/GlobalStateProvider";
import {
  HospitalFavoriteActionType,
  useHospitalFavoriteContext,
} from "@/providers/HospitalFavoriteProvider";
import {
  HospitalInfoPageType,
  HospitalStateActionType,
  useHospitalContext,
} from "@/providers/HospitalStateProvider";
import { decodeId, encodeId, scrollToTop } from "@/util";
import { logger } from "@/util/logger";

const HospitalNewTemplate = dynamic(
  () => import("@/components/templates/hospital/HospitalNewTemplate")
);

const HospitalMapTemplate = dynamic(
  () => import("@/components/templates/hospital/HospitalMapTemplate")
);

const PortalInfoComponent = dynamic(
  () => import("@/components/templates/hospital/HospitalInfoComponent")
);

const HospitalContent = styled.div`
  min-height: calc(100vh - 56px);
`;

const PreviewOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  z-index: 9999;
  pointer-events: all;
  cursor: not-allowed;
`;

type Props = {
  pageType: string;
  isPreview?: boolean;
};

const HospitalInfoContainer: React.FC<Props> = ({ pageType, isPreview }) => {
  const { query, isReady, replace, pathname } = useRouter();
  const [isLoadingPreview, setIsLoadingPreview] = useState<boolean>(
    !!isPreview
  );

  const {
    hospitalStateContext: { searchSubmenuParams, hospital },
    dispatchHospitalState,
  } = useHospitalContext();

  const {
    globalStateContext: { reserveCacheData, onlineTreatmentRouter },
  } = useGlobalContext();

  const { dispatchHospitalFavorite } = useHospitalFavoriteContext();

  const realHpId = decodeId(query.id);

  const { data: hospitalQueryData, loading } = useCustomQuery<{
    getHospitalById: PortalHospital;
  }>(GET_HOSPITAL_BY_ID, {
    skip: !isReady || !realHpId || !!hospital || isPreview,
    variables: {
      id: realHpId,
    },
    fetchPolicy: "network-only",
  });

  useEffect(() => {
    if (!!hospital || !hospitalQueryData || !hospitalQueryData.getHospitalById)
      return;

    dispatchHospitalState({
      type: HospitalStateActionType.UPDATE_HOSPITAL_INFO,
      payload: hospitalQueryData.getHospitalById,
    });
    dispatchHospitalState({
      type: HospitalStateActionType.UPDATE_STAFFS,
      payload: hospitalQueryData.getHospitalById.hospitalStaffs,
    });
    dispatchHospitalState({
      type: HospitalStateActionType.UPDATE_NEWS,
      payload: hospitalQueryData.getHospitalById.notifications,
    });
  }, [dispatchHospitalState, hospital, hospitalQueryData]);

  useEffect(() => {
    if (!isPreview) return;

    let port: MessagePort | null = null;

    const handleMessage = (event: MessageEvent) => {
      // For preview mode, we accept messages from parent window
      // MessageChannel provides the security boundary
      console.log("event------------------", event);
      if (event.data?.type === "CONNECT" && event.ports?.[0]) {
        port = event.ports[0];
        console.log("Port established, setting up onmessage handler");
        port.onmessage = (msgEvent: MessageEvent) => {
          console.log("Child received message via port:", msgEvent.data);
          const data = msgEvent.data;
          if (data?.type === "SET_DATA") {
            try {
              const payload = data.payload;
              const currentHospital = hospital;

              dispatchHospitalState({
                type: HospitalStateActionType.UPDATE_HOSPITAL_INFO,
                payload: {
                  ...(currentHospital || {}),
                  ...payload,
                },
              });
              dispatchHospitalState({
                type: HospitalStateActionType.UPDATE_STAFFS,
                payload: payload.hospitalStaffs,
              });

              setTimeout(() => {
                dispatchHospitalState({
                  type: HospitalStateActionType.PAGE_TYPE,
                  payload: payload.pageType,
                });
              }, 100);

              setIsLoadingPreview(false);
            } catch (error) {
              logger.error(error);
              setIsLoadingPreview(false);
            }
          }
        };

        // Send READY message back to parent
        setTimeout(() => {
          console.log("Sending READY message to parent");
          port?.postMessage({ type: "READY" });
        }, 100);
      }
    };

    window.addEventListener("message", handleMessage);

    // Set timeout for loading state
    const timeoutId = setTimeout(() => {
      setIsLoadingPreview(false);
    }, 3000);

    return () => {
      window.removeEventListener("message", handleMessage);
      clearTimeout(timeoutId);
      if (port) {
        port.close();
      }
    };
  }, [isPreview, dispatchHospitalState, hospital]);

  useEffect(() => {
    if (!hospital) return;

    if (pathname === PAGE_PATH.HOSPITAL_INDEX) {
      const {
        treatmentDepartmentId,
        treatmentCategoryId,
        treatmentMethodNotExpect,
      } = reserveCacheData;
      if (treatmentDepartmentId) {
        dispatchHospitalState({
          type: HospitalStateActionType.PAGE_TYPE,
          payload: HospitalInfoPageType.SUBMENU,
        });
      } else {
        scrollToTop();
      }
      dispatchHospitalState({
        type: HospitalStateActionType.UPDATE_SUBMENU_SEARCH_PARAMS,
        payload: {
          hospitalId: hospital.hospitalId,
          treatmentMethodNotExpect:
            treatmentMethodNotExpect === ReservationType.IN_PERSON
              ? ReservationType.IN_PERSON
              : undefined,
          treatmentCategoryId:
            treatmentCategoryId !== null
              ? Number(treatmentCategoryId)
              : undefined,
        },
      });
    } else {
      dispatchHospitalState({
        type: HospitalStateActionType.UPDATE_SUBMENU_SEARCH_PARAMS,
        payload: {
          hospitalId: hospital.hospitalId,
        },
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hospital]);

  useEffect(() => {
    if (!!query.id && !realHpId) replace(PAGE_PATH.NOT_FOUND);
    // eslint-disable-next-line
  }, [query.id]);

  useEffect(() => {
    let isFavorite = false;
    if (hospitalQueryData && hospitalQueryData.getHospitalById) {
      isFavorite = hospitalQueryData.getHospitalById.isFavorite;
    } else if (hospital) {
      isFavorite = hospital.isFavorite;
    }
    dispatchHospitalFavorite({
      type: HospitalFavoriteActionType.SET_FAVORITE,
      payload: isFavorite,
    });
  }, [hospital, hospitalQueryData, dispatchHospitalFavorite]);

  const updateHospitalStateFromParams = useCallback(() => {
    if (!pathname.includes(PAGE_PATH.HOSPITAL_INDEX)) return;
    if (!hospital || !onlineTreatmentRouter.pageType) return;

    dispatchHospitalState({
      type: HospitalStateActionType.UPDATE_SEARCH_PARAMS,
      payload: {
        pageType: onlineTreatmentRouter.pageType,
        searchSubmenuParams: {
          ...searchSubmenuParams,
          hospitalId: hospital.hospitalId,
          treatmentCategoryId: Number(
            onlineTreatmentRouter.treatmentCategoryId
          ),
          treatmentMethodNotExpect: onlineTreatmentRouter.isOnline
            ? TREATMENT_DEPARTMENT_METHOD_TYPE.IN_PERSON
            : undefined,
        },
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hospital, onlineTreatmentRouter]);

  useEffect(() => {
    updateHospitalStateFromParams();
  }, [updateHospitalStateFromParams]);

  const handleShareHospital = useCallback(async () => {
    const path = PAGE_PATH.HOSPITAL_INDEX;
    let hospitalData: PortalHospital | undefined;

    if (hospitalQueryData && hospitalQueryData.getHospitalById) {
      hospitalData = hospitalQueryData.getHospitalById;
    } else if (hospital) {
      hospitalData = hospital;
    }

    if (hospitalData) {
      const id = encodeId(hospitalData.hospitalId);

      const sharedData = {
        title: hospitalData.name,
        url: `${window.location.origin}${path.replace("[id]", id)}`,
      };

      try {
        await navigator.share(sharedData);
      } catch (e) {
        logger.log(e);
      }
    }
  }, [hospitalQueryData, hospital]);

  const contentPage = useMemo(() => {
    switch (pageType) {
      case "NEWS":
        return <HospitalNewTemplate />;

      case "MAP":
        return <HospitalMapTemplate />;

      default:
        return <PortalInfoComponent onShare={handleShareHospital} />;
    }
  }, [pageType, handleShareHospital]);

  if (loading || isLoadingPreview) return <LoadingOverlay />;

  return (
    <>
      {isPreview && <PreviewOverlay />}
      <HospitalContent>{hospital && contentPage}</HospitalContent>
    </>
  );
};

export default HospitalInfoContainer;
