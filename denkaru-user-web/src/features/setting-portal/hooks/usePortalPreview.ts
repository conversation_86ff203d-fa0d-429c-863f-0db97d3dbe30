import { useCallback, useEffect, useRef, useState } from "react";

import type { RefObject } from "react";

export const usePortalPreview = (
  iframeRef: RefObject<HTMLIFrameElement | null>,
  previewKey?: number,
) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const messageChannelRef = useRef<MessageChannel | null>(null);

  useEffect(() => {
    const iframeCurrent = iframeRef.current;
    if (!iframeCurrent) {
      setIsConnected(false);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    let messageChannel: MessageChannel | null = null;

    const handleIframeLoad = () => {
      // Close previous MessageChannel if exists
      if (messageChannelRef.current?.port1) {
        messageChannelRef.current.port1.close();
      }

      // Create new MessageChannel
      messageChannel = new MessageChannel();
      messageChannelRef.current = messageChannel;

      // Set up port1 message handler
      messageChannel.port1.onmessage = (event: MessageEvent) => {
        if (event.data?.type === "READY") {
          setIsConnected(true);
          setIsLoading(false);
        }
      };

      // Send CONNECT message with port2 to iframe
      // Always use "*" for targetOrigin when using MessageChannel to avoid CORS issues
      setTimeout(() => {
        if (iframeCurrent?.contentWindow && messageChannel) {
          iframeCurrent.contentWindow.postMessage({ type: "CONNECT" }, "*", [
            messageChannel.port2,
          ]);
        }
      }, 100);
    };

    // Add load event listener
    iframeCurrent.addEventListener("load", handleIframeLoad);

    // Handle case where iframe is already loaded
    if (iframeCurrent.contentDocument?.readyState === "complete") {
      handleIframeLoad();
    }

    // Set timeout for connection
    const timeoutId = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => {
      clearTimeout(timeoutId);
      iframeCurrent.removeEventListener("load", handleIframeLoad);

      // Close MessageChannel
      if (messageChannel?.port1) {
        messageChannel.port1.close();
      }

      setIsConnected(false);
      setIsLoading(false);
      messageChannelRef.current = null;
    };
  }, [iframeRef, previewKey]);

  const postData = useCallback(
    (data: unknown) => {
      if (!isConnected || !messageChannelRef.current?.port1) {
        return;
      }

      messageChannelRef.current.port1.postMessage({
        type: "SET_DATA",
        payload: data,
      });
    },
    [isConnected],
  );

  return { postData, isLoading, isConnected };
};
