import React, { useState } from "react";

import dynamic from "next/dynamic";
import Link from "next/link";
import styled from "styled-components";
import { Row } from "antd";

import { SvgIconTestCollaborationWhite } from "@/components/ui/Icon/IconTestCollaborationWhite";
import { SvgIconReceiptWhite } from "@/components/ui/Icon/IconReceiptWhite";
import { SvgIconPrintoutWhite } from "@/components/ui/Icon/IconPrintoutWhite";
import ClinicMapLogo from "@/components/ui/Icon/ClinicMapLogo";
import { SvgIconPatientListGray } from "@/components/ui/Icon/IconPatientListGray";
import { usePatientSearch } from "@/hooks/usePatientSearchForm";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { SvgIconLogo } from "@/components/ui/Icon/IconLogo";
import { Button } from "@/components/ui/NewButton";

const PatientHeaderSearchModal = dynamic(() =>
  import(
    "@/components/common/RootLayout/ClinicLayout/PatientHeaderSearchModal"
  ).then((mod) => mod.PatientHeaderSearchModal),
);

const SidebarContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 40px;
  padding: 20px;
  background-color: #005bac;
  border-top-right-radius: 12px;
`;

const BaseNavigationButton = styled(Link)`
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: background-color 0.2s ease;
`;

const PrimaryNavigationButton = styled(BaseNavigationButton)`
  width: 100%;
  height: 48px;
  background-color: white;
  border-radius: 10px;
  font-size: 18px;
  font-weight: bold;
  color: #333;

  &:hover {
    background-color: #f5f5f5;
    color: #333;
  }

  svg {
    height: 32px;
  }
`;

const SecondaryNavigationButton = styled(BaseNavigationButton)`
  flex: 1;
  height: 48px;
  background-color: #fff;
  border-radius: 8px;
  font-size: 14px;
  color: #243544;
  letter-spacing: -1px;

  &:hover {
    background-color: #f5f5f5;
    color: #243544;
  }

  span {
    line-height: 1;
  }
`;

const ActionButton = styled(BaseNavigationButton)`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  flex: 1;
  height: 40px;
  font-size: 14px;
  background-color: #329ce7;
  border-radius: 8px;
  color: #fff;

  &:hover {
    background-color: #2a8bc7;
    color: #fff;
  }

  span {
    line-height: 1;
  }
`;

const PatientListNavigationButton = styled.button`
  width: 208px;
  height: 40px;
  background-color: #329ce7;
  border-radius: 8px;
  color: white;
  overflow: hidden;
  border: none;
  display: flex;
  align-items: center;
  cursor: pointer;
  &:hover {
    background-color: #2a8bc7;
    color: white;
  }
`;

const PatientListIcon = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  background-color: #fff;
`;

const PatientListLabel = styled.span`
  flex: 1;
  text-align: center;
  font-size: 14px;
  line-height: 1;
`;

const NewRegistrationButton = styled(Button)`
  width: 120px;
  height: 36px;
  border-radius: 24px;
  font-weight: normal;
`;

const ButtonGroupRow = styled.div`
  width: 100%;
  display: flex;
  gap: 20px;
`;

const PatientManagementRow = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const StartPageSidebar = () => {
  const [isPatientModalOpen, setIsPatientModalOpen] = useState(false);
  const {
    control,
    loading,
    onSubmit,
    onFetchMore,
    onForceFetchMore,
    patients,
    handleResetValue,
    handleSetValue,
  } = usePatientSearch();
  const { handleOpenModal, handleSetPatientProps } = usePatientContext();

  const handleOpenPatientModal = () => {
    setIsPatientModalOpen(true);
    onSubmit();
  };
  const handleClosePatientModal = () => {
    setIsPatientModalOpen(false);
    handleResetValue();
  };

  return (
    <SidebarContainer>
      <Row gutter={[0, 20]}>
        <PrimaryNavigationButton href="/setting/portal/news/">
          <ClinicMapLogo />
        </PrimaryNavigationButton>

        <ButtonGroupRow>
          <SecondaryNavigationButton href="/start">
            <span>オンライン診療</span>
          </SecondaryNavigationButton>
          <SecondaryNavigationButton href="/survey-answers">
            <span>WEB問診</span>
          </SecondaryNavigationButton>
          <SecondaryNavigationButton href="/ai-assist">
            <span>AIアシスト</span>
          </SecondaryNavigationButton>
        </ButtonGroupRow>

        <PrimaryNavigationButton href="/reception">
          <SvgIconLogo />
        </PrimaryNavigationButton>
      </Row>

      <Row gutter={[0, 20]}>
        <PatientManagementRow>
          <PatientListNavigationButton
            type="button"
            onClick={handleOpenPatientModal}
          >
            <PatientListIcon>
              <SvgIconPatientListGray />
            </PatientListIcon>
            <PatientListLabel>患者リスト</PatientListLabel>
          </PatientListNavigationButton>
          <NewRegistrationButton
            varient="secondary"
            onClick={() => {
              handleSetPatientProps({ initialStep: "ONLY_ADD_PATIENT" });
              handleOpenModal("NEW_PATIENT");
            }}
          >
            新規登録
          </NewRegistrationButton>
        </PatientManagementRow>

        <ButtonGroupRow>
          <ActionButton href="/reception">
            <span>受付一覧</span>
          </ActionButton>
          <ActionButton href="/calendar">
            <span>予約管理</span>
          </ActionButton>
        </ButtonGroupRow>

        <ButtonGroupRow>
          <ActionButton href="/request-exam">
            <SvgIconTestCollaborationWhite />
            <span>検査連携</span>
          </ActionButton>
          <ActionButton href="/receipts">
            <SvgIconReceiptWhite />
            <span>レセプト</span>
          </ActionButton>
          <ActionButton href="/setting/ledger">
            <SvgIconPrintoutWhite />
            <span>帳票印刷</span>
          </ActionButton>
        </ButtonGroupRow>
      </Row>
      <PatientHeaderSearchModal
        isOpen={isPatientModalOpen}
        onClose={handleClosePatientModal}
        loading={loading}
        control={control}
        onSubmit={onSubmit}
        onFetchMore={onFetchMore}
        onForceFetchMore={onForceFetchMore}
        patients={patients}
        handleSetValue={handleSetValue}
        handleResetValue={handleResetValue}
      />
    </SidebarContainer>
  );
};
